{
  "include": [
    "**/*.ts",
    "**/*.tsx"
  ],
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "~/*": [
        "src/*"
      ],
      "~/components/*": [
        "src/core/components/*"
      ],
      "~/utils/*": [
        "src/core/utils/*"
      ],
      "~/user/*": [
        "src/modules/user/*"
      ],
      "~/auth/*": [
        "src/modules/auth/*"
      ],
      "~/category/*": [
        "src/modules/category/*"
      ],
      "~/brand/*": [
        "src/modules/brand/*"
      ],
      "~/measurement-unit/*": [
        "src/modules/measurement-unit/*"
      ],
      "~/container/*": [
        "src/modules/container/*"
      ],
      "~/product/*": [
        "src/modules/product/*"
      ],
      "~/person/*": [
        "src/modules/person/*"
      ],
      "~/work-area/*": [
        "src/modules/work-area/*"
      ],
      "~/operation/*": [
        "src/modules/operation/*"
      ],
      "~/production-flow/*": [
        "src/modules/production-flow/*"
      ],
      "~/materials/*": [
        "src/modules/materials/*"
      ],
      "~/suppliers/*": [
        "src/modules/suppliers/*"
      ],
      "~/raw-materials/*": [
        "src/modules/raw-materials/*"
      ],
      "~/production-devices/*": [
        "src/modules/production-devices/*"
      ],
      "~/recipe/*": [
        "src/modules/recipe/*"
      ],
      "~/warehouse/*": [
        "src/modules/warehouse/*"
      ]
    },
    "jsx": "react-jsx",
    "moduleResolution": "Bundler",
    "module": "ESNext",
    "target": "ES2022",
    "skipLibCheck": true,
    "strictNullChecks": true,
  }
}