import { useQuery } from "@tanstack/react-query";
import { Calculator, Hash, Ruler, Settings, Tag, Type } from "lucide-react";
import { useService } from "src/config/context/serviceProvider";
import { AppRuntime } from "src/core/service/utils/runtimes";
import { unitMeasurementCategoryOptions } from "../../hooks/unit-measurement-category-options";
import type { EditMeasurementUnitModalProps } from "./use-edit-measurement-unit-modal";
import useEditMeasurementUnitModal from "./use-edit-measurement-unit-modal";

export default function EditMeasurementUnitForm({
	measurementUnit,
	setIsOpen,
}: Omit<EditMeasurementUnitModalProps, "isOpen">) {
	const service = useService();
	const { measurementUnit: measurementUnitService } = service;
	const { form } = useEditMeasurementUnitModal({
		isOpen: true,
		setIsOpen,
		measurementUnit,
	});

	const { data: unitMeasurementCategories = [] } = useQuery(
		unitMeasurementCategoryOptions(service),
	);

	return (
		<form
			onSubmit={(e) => {
				e.preventDefault();
				form.handleSubmit();
			}}
		>
			<form.AppForm>
				<fieldset className="fieldset">
					<form.AppField
						name="name"
						children={({ FSTextField }) => (
							<FSTextField
								label="Nombre"
								placeholder="Nombre de la unidad de medida"
								prefixComponent={<Ruler size={16} />}
							/>
						)}
					/>
					<form.AppField
						name="code"
						validators={{
							onChangeAsyncDebounceMs: 500,
							onChangeAsync: async ({ value }) => {
								if (
									!value ||
									value.trim() === "" ||
									value === measurementUnit.code
								) {
									return undefined;
								}
								try {
									await AppRuntime.runPromise(
										measurementUnitService.validateCode(value),
									);
									return undefined;
								} catch (e) {
									return [{ message: "El código ya existe" }];
								}
							},
						}}
						children={({ FSTextField }) => (
							<FSTextField
								label="Código"
								placeholder="Código de la unidad de medida"
								prefixComponent={<Hash size={16} />}
							/>
						)}
					/>
					<form.AppField
						name="unitMeasurementCategoryId"
						children={({ FSSelectField }) => (
							<FSSelectField
								label="Categoría de Unidad"
								placeholder="Seleccionar categoría"
								options={unitMeasurementCategories.map((category) => ({
									value: category.id,
									label: category.name,
								}))}
							/>
						)}
					/>
					<form.AppField
						name="abbreviation"
						children={({ FSTextField }) => (
							<FSTextField
								label="Abreviación"
								placeholder="Abreviación de la unidad"
								prefixComponent={<Tag size={16} />}
							/>
						)}
					/>
					<form.AppField
						name="type"
						children={({ FSTextField }) => (
							<FSTextField
								label="Tipo"
								placeholder="Tipo de unidad"
								prefixComponent={<Type size={16} />}
							/>
						)}
					/>
					<form.AppField
						name="conversionFactor"
						children={({ FSTextField }) => (
							<FSTextField
								label="Factor de Conversión"
								placeholder="Factor de conversión"
								type="number"
								prefixComponent={<Calculator size={16} />}
							/>
						)}
					/>
					<form.AppField
						name="state"
						children={({ FSTextField }) => (
							<FSTextField
								label="Estado"
								placeholder="Estado de la unidad"
								prefixComponent={<Settings size={16} />}
							/>
						)}
					/>
				</fieldset>
				<div className="modal-action">
					<form.SubscribeButton
						label="Actualizar"
						className="btn btn-primary"
					/>
				</div>
			</form.AppForm>
		</form>
	);
}
