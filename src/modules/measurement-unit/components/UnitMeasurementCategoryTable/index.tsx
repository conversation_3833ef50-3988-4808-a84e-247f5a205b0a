import { useQuery } from "@tanstack/react-query";
import { useState } from "react";
import { useService } from "src/config/context/serviceProvider";
import { measurementUnitOptions } from "../../hooks/measurement-unit-options";
import { unitMeasurementCategoryOptions } from "../../hooks/unit-measurement-category-options";
import type { MeasurementUnit } from "../../service/model/measurement-unit";
import EditMeasurementUnitModal from "../EditMeasurementUnitModal";

export default function UnitMeasurementCategoryTable() {
	const service = useService();
	const [selectedMeasurementUnit, setSelectedMeasurementUnit] =
		useState<MeasurementUnit | null>(null);
	const [isEditOpen, setIsEditOpen] = useState(false);

	const { data: unitMeasurementCategories = [], isLoading: categoriesLoading } =
		useQuery(unitMeasurementCategoryOptions(service));

	const { data: measurementUnits = [], isLoading: unitsLoading } = useQuery(
		measurementUnitOptions(service),
	);

	const handleEditMeasurementUnit = (measurementUnit: MeasurementUnit) => {
		setSelectedMeasurementUnit(measurementUnit);
		setIsEditOpen(true);
	};

	if (categoriesLoading || unitsLoading) {
		return (
			<div className="flex h-32 items-center justify-center">
				<span className="loading loading-spinner loading-lg" />
			</div>
		);
	}

	return (
		<>
			<div className="overflow-x-auto">
				<table className="table-zebra table">
					<thead>
						<tr>
							<th>Categoría</th>
							<th>Unidades de Medida</th>
						</tr>
					</thead>
					<tbody>
						{unitMeasurementCategories.map((category) => {
							const categoryUnits = measurementUnits.filter(
								(unit) => unit.unitMeasurementCategoryId === category.id,
							);

							return (
								<tr key={category.id}>
									<td>
										<div>
											<div className="font-bold">{category.name}</div>
											<div className="text-sm opacity-50">{category.code}</div>
										</div>
									</td>
									<td>
										<div className="flex flex-wrap gap-2">
											{categoryUnits.length > 0 ? (
												categoryUnits.map((unit) => (
													<button
														key={unit.id}
														type="button"
														className="badge badge-primary hover:badge-secondary cursor-pointer"
														onClick={() => handleEditMeasurementUnit(unit)}
													>
														{unit.abbreviation || unit.name}
													</button>
												))
											) : (
												<span className="text-sm opacity-50">Sin unidades</span>
											)}
										</div>
									</td>
								</tr>
							);
						})}
					</tbody>
				</table>
			</div>

			{selectedMeasurementUnit && (
				<EditMeasurementUnitModal
					isOpen={isEditOpen}
					setIsOpen={setIsEditOpen}
					measurementUnit={selectedMeasurementUnit}
				/>
			)}
		</>
	);
}
