import { useMutation, useQueryClient } from "@tanstack/react-query";
import { create } from "mutative";
import { useService } from "src/config/context/serviceProvider";
import { AppRuntime } from "src/core/service/utils/runtimes";
import type {
	MeasurementUnit,
	UpdateMeasurementUnit,
} from "../service/model/measurement-unit";
import { measurementUnitOptions } from "./measurement-unit-options";

export default function useUpdateMeasurementUnit() {
	const service = useService();
	const { measurementUnit } = service;
	const queryClient = useQueryClient();
	const queryKey = measurementUnitOptions(service).queryKey;

	return useMutation({
		mutationKey: ["update-measurement-unit"],
		mutationFn: (updatedMeasurementUnit: UpdateMeasurementUnit) =>
			AppRuntime.runPromise(measurementUnit.update(updatedMeasurementUnit)),
		onMutate: async (updatedMeasurementUnit) => {
			await queryClient.cancelQueries({ queryKey });

			const previousMeasurementUnits = queryClient.getQueryData(queryKey);

			if (previousMeasurementUnits) {
				queryClient.setQueryData(
					queryKey,
					create(previousMeasurementUnits, (draft) => {
						const index = draft.findIndex(
							(item: MeasurementUnit) => item.id === updatedMeasurementUnit.id,
						);
						if (index !== -1) {
							draft[index] = {
								...draft[index],
								name: updatedMeasurementUnit.name,
								code: updatedMeasurementUnit.code,
								unitMeasurementCategoryId: updatedMeasurementUnit.unitMeasurementCategoryId || draft[index].unitMeasurementCategoryId,
								abbreviation: updatedMeasurementUnit.abbreviation || draft[index].abbreviation,
								type: updatedMeasurementUnit.type || draft[index].type,
								conversionFactor: updatedMeasurementUnit.conversionFactor || draft[index].conversionFactor,
								state: updatedMeasurementUnit.state || draft[index].state,
							};
						}
					}),
				);
			}

			return { previousMeasurementUnits };
		},
		onError: (_err, _updatedMeasurementUnit, context) => {
			queryClient.setQueryData(queryKey, context?.previousMeasurementUnits);
		},
		onSettled: () => {
			queryClient.invalidateQueries({ queryKey });
		},
	});
}
