import { Schema } from "effect";

export const MeasurementUnit = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	code: Schema.String,
	unitMeasurementCategoryId: Schema.String,
	abbreviation: Schema.String,
	type: Schema.String,
	conversionFactor: Schema.Number,
	state: Schema.String,
	createdAt: Schema.NullOr(Schema.String),
	updatedAt: Schema.NullOr(Schema.String),
	deletedAt: Schema.NullOr(Schema.String),
});
export type MeasurementUnit = typeof MeasurementUnit.Type;

export const CreateMeasurementUnit = Schema.Struct({
	name: Schema.String,
	code: Schema.String,
});
export type CreateMeasurementUnit = typeof CreateMeasurementUnit.Type;

export const UpdateMeasurementUnit = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	code: Schema.String,
});
export type UpdateMeasurementUnit = typeof UpdateMeasurementUnit.Type;
