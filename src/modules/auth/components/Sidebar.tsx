import { Link } from "@tanstack/react-router";
import { useStore } from "@tanstack/react-store";
import {
	Boxes,
	Building2,
	ChevronLeft,
	ChevronRight,
	Factory,
	Package,
	PackageSearch,
	Shield,
	ShoppingCart,
	Store,
	Truck,
	Users,
	Warehouse,
} from "lucide-react";
import { useState } from "react";
import { sidebarActions, sidebarIsCollapsed } from "../store/sidebar";

export default function Sidebar() {
	const isCollapsed = useStore(sidebarIsCollapsed);
	const [openGroups, setOpenGroups] = useState<string[]>([]);

	const toggleGroup = (groupName: string) => {
		setOpenGroups((prev) =>
			prev.includes(groupName)
				? prev.filter((name) => name !== groupName)
				: [...prev, groupName],
		);
	};

	const menuGroups = [
		{
			name: "Datos maestros",
			icon: Package,
			items: [
				{ name: "categorias", icon: Boxes, to: "/admin/products/categories" },
				{ name: "marcas", icon: Store, to: "/admin/products/brands" },
				{
					name: "unidades de medida",
					icon: Store,
					to: "/admin/products/measurement-units",
				},
				{
					name: "productos comerciales",
					icon: PackageSearch,
					to: "/admin/products/products",
				},
				{ name: "insumos", icon: Truck, to: "/admin/products/suppliers" },
				{
					name: "materiales",
					icon: Warehouse,
					to: "/admin/products/materials",
				},
				{
					name: "materias primas",
					icon: Boxes,
					to: "/admin/products/raw-materials",
				},
				{
					name: "equipos de producción",
					icon: Warehouse,
					to: "/admin/products/production-devices",
				},
			],
		},
		{
			name: "Produccion",
			icon: Factory,
			items: [
				{ name: "recetas", icon: Package, to: "/admin/manufacture/recipes" },
				{
					name: "flujo de producción",
					icon: Building2,
					to: "/admin/manufacture/production-flow",
				},
				{
					name: "areas de trabajo",
					icon: Warehouse,
					to: "/admin/manufacture/work-area",
				},
				{
					name: "operaciones",
					icon: ShoppingCart,
					to: "/admin/manufacture/operations",
				},
				{
					name: "simulación de producción",
					icon: Package,
					to: "/admin/manufacture/recipe-test",
				},
			],
		},
		{
			name: "ventas",
			icon: ShoppingCart,
			items: [{ name: "ventas", icon: ShoppingCart, to: "/admin/sales" }],
		},
		{
			name: "clientes",
			icon: Users,
			items: [{ name: "clientes", icon: Users, to: "/admin/clients" }],
		},
		{
			name: "inventarios",
			icon: Warehouse,
			items: [
				{ name: "inventario", icon: Warehouse, to: "/admin/inventory" },
				{ name: "Almacenes", icon: Boxes, to: "/admin/inventory/warehouses" },
			],
		},
		{
			name: "seguridad",
			icon: Shield,
			items: [{ name: "usuarios", icon: Users, to: "/admin/security/users" }],
		},
	];

	return (
		<div
			className={`h-full flex-shrink-0 bg-base-300 shadow-lg transition-all duration-300 ${
				isCollapsed ? "w-16" : "w-64"
			}`}
		>
			<div className="flex h-full flex-col">
				<div className="flex items-center justify-between p-4">
					{!isCollapsed && (
						<h2 className="font-bold text-2xl text-primary">Fhyona</h2>
					)}
					<button
						type="button"
						onClick={sidebarActions.toggleCollapsed}
						className="btn btn-ghost btn-sm"
					>
						{isCollapsed ? (
							<ChevronRight className="h-5 w-5" />
						) : (
							<ChevronLeft className="h-5 w-5" />
						)}
					</button>
				</div>

				<div className="flex-1 overflow-y-auto p-2">
					{menuGroups.map((group) => (
						<div key={group.name} className="mb-2">
							<button
								type="button"
								onClick={() => toggleGroup(group.name)}
								className="flex w-full items-center gap-3 rounded-lg px-3 py-2 text-left hover:bg-base-200"
							>
								<group.icon className="h-5 w-5" />
								{!isCollapsed && (
									<span className="capitalize">{group.name}</span>
								)}
							</button>

							{!isCollapsed && openGroups.includes(group.name) && (
								<div className="mt-1 space-y-1 pl-8">
									{group.items.map((item) => (
										<Link
											key={item.name}
											to={item.to}
											className="flex items-center gap-3 rounded-lg px-3 py-2 hover:bg-base-200"
										>
											<item.icon className="h-4 w-4" />
											<span className="capitalize">{item.name}</span>
										</Link>
									))}
								</div>
							)}
						</div>
					))}
				</div>
			</div>
		</div>
	);
}
