import { useQuery } from "@tanstack/react-query";
import { Calculator, RotateCcw } from "lucide-react";
import { useEffect, useState } from "react";
import { useService } from "~/config/context/serviceProvider";
import { useAppForm } from "~/core/components/form/form";
import { getErrorResult } from "~/core/utils/effectErrors";
import { CategoryCode } from "~/modules/category/service/model/category";
import { productOptionsByCategoryCode } from "~/modules/product/hooks/product-options";
import type { Product } from "~/modules/product/service/model/product";
import { ProductionCalculationSchema } from "~/modules/production-order-simulation/components/ProductionOrderSimulation/schema";
import type { ProductionCalculation } from "~/modules/production-order-simulation/components/ProductionOrderSimulation/types";
import { recipeOptions } from "~/modules/recipe/hooks/recipe-options";
import type { Recipe } from "~/modules/recipe/service/model/recipe";

export default function ProductionOrderSimulation() {
	const service = useService();
	const [calculation, setCalculation] = useState<ProductionCalculation | null>(
		null,
	);

	const {
		data: products = [],
		isPending: isLoadingProducts,
		isError: isProductsError,
		error: productsError,
	} = useQuery(productOptionsByCategoryCode(service, CategoryCode.PRODUCTS));

	const {
		data: materials = [],
		isPending: isLoadingMaterials,
		isError: isMaterialsError,
		error: MaterialsError,
	} = useQuery(productOptionsByCategoryCode(service, CategoryCode.MATERIALS));

	const {
		data: recipes = [],
		isPending: isLoadingRecipes,
		isError: isRecipesError,
		error: recipesError,
	} = useQuery(recipeOptions(service));

	useEffect(() => {
		if (productsError) {
			console.log(getErrorResult(productsError).error);
		}
		if (recipesError) {
			console.log(getErrorResult(recipesError).error);
		}
	}, [productsError, recipesError]);

	const form = useAppForm({
		defaultValues: {
			productId: "",
			quantity: 1,
		},
		validators: {
			onChange: ProductionCalculationSchema,
		},
		onSubmit: ({ value }) => {
			const selectedProduct = products.find((p) => p.id === value.productId);
			if (!selectedProduct) return;

			const result = calculateProduction(
				selectedProduct,
				value.quantity,
				recipes,
			);
			setCalculation(result);
		},
	});

	const resetForm = () => {
		form.reset();
		setCalculation(null);
	};

	// Handle empty products state
	if (!isLoadingProducts && !isProductsError && products.length === 0) {
		return (
			<div className="container mx-auto">
				<div className="card bg-base-100 shadow-xl">
					<div className="card-body">
						<div className="alert alert-info">
							<div>
								<h4 className="font-semibold">No hay productos disponibles</h4>
								<p>
									No se encontraron productos en el sistema. Debe crear
									productos antes de poder simular órdenes de producción.
								</p>
							</div>
						</div>
					</div>
				</div>
			</div>
		);
	}

	// Handle loading states
	if (isLoadingProducts || isLoadingRecipes) {
		return (
			<div className="container mx-auto">
				<div className="card bg-base-100 shadow-xl">
					<div className="card-body">
						<div className="flex justify-center p-8">
							<span className="loading loading-spinner loading-lg" />
						</div>
					</div>
				</div>
			</div>
		);
	}

	// Handle error states
	if (isProductsError || isRecipesError) {
		return (
			<div className="container mx-auto">
				<div className="card bg-base-100 shadow-xl">
					<div className="card-body">
						<div className="alert alert-error">
							<span>
								Error al cargar datos:{" "}
								{isProductsError && productsError
									? getErrorResult(productsError).error.message
									: isRecipesError && recipesError
										? getErrorResult(recipesError).error.message
										: "Error desconocido"}
							</span>
						</div>
					</div>
				</div>
			</div>
		);
	}

	return (
		<div className="container mx-auto space-y-6">
			<div className="card bg-base-100 shadow-xl">
				<div className="card-body">
					<h2 className="card-title mb-6 text-2xl">
						Simulación de Orden de Producción
					</h2>
					<form
						onSubmit={(e) => {
							e.preventDefault();
							form.handleSubmit();
						}}
					>
						<form.AppForm>
							<div className="mb-6 grid grid-cols-1 gap-4 md:grid-cols-2">
								<form.AppField
									name="productId"
									children={({ FSComboBoxField }) => (
										<FSComboBoxField
											label="Producto"
											placeholder="Seleccione un producto"
											options={products.map((product) => ({
												value: product.id,
												label: `${product.name} (${product.code})`,
											}))}
											isLoading={isLoadingProducts}
										/>
									)}
								/>

								<form.AppField
									name="quantity"
									children={({ FSTextField }) => (
										<FSTextField
											label="Cantidad"
											placeholder="Ingrese la cantidad"
											type="number"
										/>
									)}
								/>
							</div>

							<div className="flex gap-4">
								<form.SubscribeButton label="Calcular" />
								<button
									type="button"
									className="btn btn-ghost"
									onClick={resetForm}
								>
									<RotateCcw size={16} />
									Limpiar
								</button>
							</div>
						</form.AppForm>
					</form>
				</div>
			</div>

			{calculation && (
				<div className="card bg-base-100 shadow-xl">
					<div className="card-body">
						<h3 className="card-title mb-4 text-xl">Resultados del Cálculo</h3>

						{calculation.recipeName && (
							<div className="alert alert-info mb-4">
								<div>
									<h4 className="font-semibold">
										Receta: {calculation.recipeName}
									</h4>
									{calculation.batchesNeeded && (
										<p>Lotes necesarios: {calculation.batchesNeeded}</p>
									)}
								</div>
							</div>
						)}

						{calculation.components.length > 0 && (
							<div className="mb-6">
								<h4 className="mb-3 font-semibold text-lg">
									Componentes de Receta
								</h4>
								<div className="overflow-x-auto">
									<table className="table-zebra table w-full">
										<thead>
											<tr>
												<th>Componente</th>
												<th>Cantidad por Unidad/Lote</th>
												<th>Cantidad Total</th>
											</tr>
										</thead>
										<tbody>
											{calculation.components.map((component) => (
												<tr key={component.productId}>
													<td>{component.productName}</td>
													<td>{component.quantity}</td>
													<td className="font-semibold">
														{component.totalQuantity}
													</td>
												</tr>
											))}
										</tbody>
									</table>
								</div>
							</div>
						)}

						{calculation.materials.length > 0 && (
							<div>
								<h4 className="mb-3 font-semibold text-lg">
									Materiales de Producción
								</h4>
								<div className="overflow-x-auto">
									<table className="table-zebra table w-full">
										<thead>
											<tr>
												<th>Material ID</th>
												<th>Cantidad por Unidad</th>
												<th>Cantidad Total</th>
											</tr>
										</thead>
										<tbody>
											{calculation.materials.map((material) => (
												<tr key={material.productId}>
													<td>
														{
															materials.find((m) => m.id === material.productId)
																?.name
														}
													</td>
													<td>{material.quantity}</td>
													<td className="font-semibold">
														{material.totalQuantity}
													</td>
												</tr>
											))}
										</tbody>
									</table>
								</div>
							</div>
						)}

						{calculation.components.length === 0 &&
							calculation.materials.length === 0 && (
								<div className="alert alert-warning">
									<div>
										<h4 className="font-semibold">
											Sin componentes ni materiales
										</h4>
										<p>
											Este producto no tiene recetas asociadas ni información de
											producción configurada. Para obtener cálculos de
											producción, asegúrese de:
										</p>
										<ul className="mt-2 list-inside list-disc space-y-1">
											<li>Crear una receta que incluya este producto</li>
											<li>
												Configurar información de producción con materiales
											</li>
										</ul>
									</div>
								</div>
							)}
					</div>
				</div>
			)}
		</div>
	);
}

function calculateProduction(
	product: Product,
	quantity: number,
	recipes: Recipe[],
): ProductionCalculation {
	const result: ProductionCalculation = {
		components: [],
		materials: [],
	};

	// Validate inputs
	if (!product || quantity <= 0) {
		return result;
	}

	// Find recipe that contains this product
	const recipe = recipes.find((r) =>
		r.products.some((p) => p.id === product.id),
	);

	if (recipe && recipe.components.length > 0) {
		result.recipeName = recipe.name;

		// Calculate batches needed for bulk recipes
		if (recipe.type === "bulk" && recipe.batchSize > 0) {
			result.batchesNeeded = quantity / recipe.batchSize;

			// Calculate components based on batches
			const batchesNeeded = result.batchesNeeded;
			result.components = recipe.components
				.filter((component) => component.quantity > 0)
				.map((component) => ({
					productId: component.product.id,
					productName: component.product.name,
					quantity: component.quantity,
					totalQuantity: component.quantity * batchesNeeded,
				}));
		} else {
			// For unit recipes, multiply components by quantity
			result.components = recipe.components
				.filter((component) => component.quantity > 0)
				.map((component) => ({
					productId: component.product.id,
					productName: component.product.name,
					quantity: component.quantity,
					totalQuantity: component.quantity / quantity,
				}));
		}
	}

	// Calculate materials from production info
	console.log(product.productionInfo?.materials);
	if (
		product.productionInfo?.materials &&
		product.productionInfo.materials.length > 0
	) {
		result.materials = product.productionInfo.materials
			.filter((material) => material.quantity > 0)
			.map((material) => ({
				productId: material.productId,
				quantity: material.quantity,
				totalQuantity: material.quantity * quantity,
			}));
	}

	return result;
}
