import * as v from "valibot";

export const ProductionCalculationSchema = v.object({
	productId: v.pipe(
		v.string("Debe seleccionar un producto"),
		v.minLength(1, "Debe seleccionar un producto"),
	),
	quantity: v.pipe(
		v.number("Debe ingresar una cantidad"),
		v.minValue(1, "La cantidad debe ser mayor a 0"),
	),
});

export type ProductionCalculationSchema = v.InferOutput<typeof ProductionCalculationSchema>;
