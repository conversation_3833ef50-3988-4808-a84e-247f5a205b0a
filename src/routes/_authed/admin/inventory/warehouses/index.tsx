import { createFileRoute } from "@tanstack/react-router";
import { useState } from "react";
import CreateWarehouseModal from "~/warehouse/components/CreateWarehouseModal";
import WarehouseTable from "~/warehouse/components/WarehouseTable";

export const Route = createFileRoute("/_authed/admin/inventory/warehouses/")({
	component: RouteComponent,
});

function RouteComponent() {
	const [isOpen, setIsOpen] = useState(false);

	return (
		<>
			<div className="container mx-auto ">
				<div className="card bg-base-300">
					<div className="card-body">
						<div>
							<button
								type="button"
								className="btn btn-primary"
								onClick={() => setIsOpen(true)}
							>
								Crear nuevo almacén
							</button>
						</div>
					</div>
					<WarehouseTable />
				</div>
			</div>
			<CreateWarehouseModal isOpen={isOpen} setIsOpen={setIsOpen} />
		</>
	);
}
