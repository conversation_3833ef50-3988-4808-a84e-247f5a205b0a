import { Link, createFileRoute } from "@tanstack/react-router";
import { ArrowLeft } from "lucide-react";
import CreateProductForm from "~/modules/product/components/CreateProductForm";
import CreateProductTabs from "~/modules/product/components/CreateProductTabs";

export const Route = createFileRoute("/_authed/admin/products/products/create")(
	{
		component: RouteComponent,
	},
);

function RouteComponent() {
	return (
		<div className="container mx-auto space-y-4">
			<div className="mb-6">
				<div className="mb-4 flex items-center gap-4">
					<Link to="/admin/products/products" className="btn btn-ghost btn-sm">
						<ArrowLeft size={16} />
						Volver
					</Link>
					<h1 className="font-bold text-2xl">Crear Producto</h1>
				</div>
			</div>
			<CreateProductTabs />
			<CreateProductForm />
		</div>
	);
}
